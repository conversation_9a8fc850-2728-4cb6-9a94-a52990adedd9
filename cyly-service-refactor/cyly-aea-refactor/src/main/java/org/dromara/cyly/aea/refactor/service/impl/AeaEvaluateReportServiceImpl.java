package org.dromara.cyly.aea.refactor.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.enums.ReasonCodeEnum;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.cyly.aea.refactor.domain.AeaEvaluateReport;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluateInfo;
import org.dromara.cyly.aea.refactor.domain.bo.AeaEvaluateReportBo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaEvaluateReportVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateExecutionRecordVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateInfoVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateVo;
import org.dromara.cyly.aea.refactor.mapper.AeaEvaluateReportMapper;
import org.dromara.cyly.aea.refactor.mapper.AeaTaskEvaluateInfoMapper;
import org.dromara.cyly.aea.refactor.mapper.AeaTaskEvaluateMapper;
import org.dromara.cyly.aea.refactor.service.IAeaEvaluateReportService;
import org.dromara.cyly.aea.refactor.service.IAeaTaskEvaluateExecutionRecordService;
import org.dromara.cyly.aea.refactor.templates.GBReportTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 评估报告
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AeaEvaluateReportServiceImpl implements IAeaEvaluateReportService {

    private final AeaEvaluateReportMapper baseMapper;
    private final IAeaTaskEvaluateExecutionRecordService executionRecordService;
    private final AeaTaskEvaluateMapper aeaTaskEvaluateMapper;
    private final AeaTaskEvaluateInfoMapper aeaTaskEvaluateInfoMapper;
    private final GBReportTemplate gbReportTemplate;

    /**
     * 查询评估报告
     */
    @Override
    public AeaEvaluateReportVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询评估报告列表
     */
    @Override
    public TableDataInfo<AeaEvaluateReportVo> queryPageList(AeaEvaluateReportBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AeaEvaluateReport> lqw = buildQueryWrapper(bo);
        Page<AeaEvaluateReportVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询评估报告列表
     */
    @Override
    public List<AeaEvaluateReportVo> queryList(AeaEvaluateReportBo bo) {
        LambdaQueryWrapper<AeaEvaluateReport> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AeaEvaluateReport> buildQueryWrapper(AeaEvaluateReportBo bo) {
        LambdaQueryWrapper<AeaEvaluateReport> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getCode()), AeaEvaluateReport::getCode, bo.getCode());
        lqw.eq(bo.getTaskId() != null, AeaEvaluateReport::getTaskId, bo.getTaskId());
        lqw.eq(bo.getElderId() != null, AeaEvaluateReport::getElderId, bo.getElderId());
        lqw.eq(bo.getAssessorEvaluateUserId() != null, AeaEvaluateReport::getAssessorEvaluateUserId, bo.getAssessorEvaluateUserId());
        lqw.eq(bo.getDeputyEvaluateUserId() != null, AeaEvaluateReport::getDeputyEvaluateUserId, bo.getDeputyEvaluateUserId());
        lqw.like(StringUtils.isNotBlank(bo.getLocation()), AeaEvaluateReport::getLocation, bo.getLocation());
        lqw.like(bo.getReasonCode()!=null, AeaEvaluateReport::getReasonCode, bo.getReasonCode());
        lqw.eq(bo.getSelfScore() != null, AeaEvaluateReport::getSelfScore, bo.getSelfScore());
        lqw.eq(bo.getBaseScore() != null, AeaEvaluateReport::getBaseScore, bo.getBaseScore());
        lqw.eq(bo.getMentionScore() != null, AeaEvaluateReport::getMentionScore, bo.getMentionScore());
        lqw.eq(bo.getFeelScore() != null, AeaEvaluateReport::getFeelScore, bo.getFeelScore());
        lqw.eq(bo.getTotalScore() != null, AeaEvaluateReport::getTotalScore, bo.getTotalScore());
        lqw.like(bo.getFirstLevel()!=null, AeaEvaluateReport::getFirstLevel, bo.getFirstLevel());
        lqw.like(StringUtils.isNotBlank(bo.getAdjustmentBasis()), AeaEvaluateReport::getAdjustmentBasis, bo.getAdjustmentBasis());
        lqw.like(bo.getFinalLevel()!=null, AeaEvaluateReport::getFinalLevel, bo.getFinalLevel());
        lqw.eq(bo.getAnswerId() != null, AeaEvaluateReport::getAnswerId, bo.getAnswerId());
        lqw.like(StringUtils.isNotBlank(bo.getContent()), AeaEvaluateReport::getContent, bo.getContent());
        lqw.eq(bo.getScore() != null, AeaEvaluateReport::getScore, bo.getScore());
        lqw.eq(bo.getEvaluateUserId() != null, AeaEvaluateReport::getEvaluateUserId, bo.getEvaluateUserId());
        lqw.eq(bo.getDelFlag() != null, AeaEvaluateReport::getDelFlag, bo.getDelFlag());
        return lqw;
    }

    /**
     * 新增评估报告
     */
    @Override
    public Boolean insertByBo(AeaEvaluateReportBo bo) {
        AeaEvaluateReport add = MapstructUtils.convert(bo, AeaEvaluateReport.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改评估报告
     */
    @Override
    public Boolean updateByBo(AeaEvaluateReportBo bo) {
        AeaEvaluateReport update = MapstructUtils.convert(bo, AeaEvaluateReport.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AeaEvaluateReport entity){
        // 可以根据需要添加校验逻辑
    }

    /**
     * 批量删除评估报告
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            // 可以添加删除前的校验逻辑
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 创建评估报告
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createEvaluateReport(AeaEvaluateReportBo reportBo) {
        // 生成报告编码
        String code = generateReportCode();
        reportBo.setCode(code);

        // 设置删除状态为否
        reportBo.setDelFlag(0);

        // 计算分数和等级
        calculateScoresAndLevel(reportBo);

        // 插入报告记录
        Boolean result = insertByBo(reportBo);
        if (!result) {
            throw new RuntimeException("创建评估报告失败");
        }

        return reportBo.getId();
    }

    /**
     * 生成PDF报告
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String generatePdfReport(Map<String, Object> map) {
        AeaEvaluateReportVo report = baseMapper.selectVoById(reportId);
        if (report == null) {
            throw new RuntimeException("评估报告不存在");
        }

        try {
            // 获取答题记录
            List<AeaTaskEvaluateExecutionRecordVo> records =
                executionRecordService.queryByTaskIdAndInfoId(report.getTaskId(), report.getId());

            // 生成PDF文件
            String pdfUrl = generatePdfFile(report, records);

            // 更新报告URL
            updateReportUrl(reportId, pdfUrl);

            return pdfUrl;
        } catch (Exception e) {
            log.error("生成PDF报告失败，报告ID: {}", reportId, e);
            throw new RuntimeException("生成PDF报告失败: " + e.getMessage());
        }
    }

    /**
     * 根据任务ID查询报告
     */
    @Override
    public AeaEvaluateReportVo queryByTaskId(Long taskId) {
        LambdaQueryWrapper<AeaEvaluateReport> lqw = Wrappers.lambdaQuery();
        lqw.eq(AeaEvaluateReport::getTaskId, taskId)
           .eq(AeaEvaluateReport::getDelFlag, 0)
           .orderByDesc(AeaEvaluateReport::getCreateTime)
           .last("LIMIT 1");

        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 更新报告URL
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateReportUrl(Long reportId, String reportUrl) {
        AeaEvaluateReport report = baseMapper.selectById(reportId);
        if (report == null) {
            throw new RuntimeException("评估报告不存在");
        }

        report.setReportUrl(reportUrl);
        return baseMapper.updateById(report) > 0;
    }

    /**
     * 获取报告下载链接
     */
    @Override
    public String getReportDownloadUrl(Long reportId) {
        AeaEvaluateReportVo report = queryById(reportId);
        if (report == null) {
            throw new RuntimeException("评估报告不存在");
        }

        if (StringUtils.isBlank(report.getReportUrl())) {
            // 如果报告URL为空，尝试生成PDF
            return generatePdfReport(reportId);
        }

        return report.getReportUrl();
    }

    @Override
    public List<AeaEvaluateReportVo> selectVoListByTaskIds(Set<Long> taskIdList) {
        if (CollectionUtils.isEmpty(taskIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AeaEvaluateReport> lqw = Wrappers.lambdaQuery();
        lqw.in(AeaEvaluateReport::getTaskId, taskIdList);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public Long selectCount(String code) {
        return baseMapper.selectCount(new QueryWrapper<AeaEvaluateReport>()
                .like("code", code));
    }

    /**
     * 生成报告编码
     */
    private String generateReportCode() {
        String dateStr = DateUtil.format(new Date(), "yyyyMMdd");
        String uuid = IdUtil.fastSimpleUUID().substring(0, 8).toUpperCase();
        return "RPT" + dateStr + uuid;
    }

    /**
     * 计算分数和等级
     */
    private void calculateScoresAndLevel(AeaEvaluateReportBo reportBo) {
        if (reportBo.getTaskId() == null) {
            return;
        }

        // 计算总分
        Integer totalScore = executionRecordService.calculateTotalScore(reportBo.getTaskId(), reportBo.getId());
        reportBo.setTotalScore(totalScore);

        // 计算各维度分数
        Map<String, Integer> dimensionScores =
            executionRecordService.calculateDimensionScores(reportBo.getTaskId(), reportBo.getId());

        // 设置各维度分数（根据实际维度调整）
        reportBo.setBaseScore(dimensionScores.getOrDefault("基础能力", 0));
        reportBo.setMentionScore(dimensionScores.getOrDefault("认知能力", 0));
        reportBo.setFeelScore(dimensionScores.getOrDefault("情感能力", 0));
        reportBo.setSelfScore(dimensionScores.getOrDefault("其他", 0));

        // 计算等级
        String levelStr = executionRecordService.calculateLevel(totalScore);
        Integer level = convertLevelToInteger(levelStr);
        reportBo.setFirstLevel(level);
        reportBo.setFinalLevel(level);
    }

    /**
     * 将等级字符串转换为整数
     */
    private Integer convertLevelToInteger(String levelStr) {
        if (StringUtils.isBlank(levelStr)) {
            return 1; // 默认为能力完好
        }

        switch (levelStr) {
            case "优秀":
            case "能力完好":
                return 1;
            case "良好":
            case "轻度受损":
                return 2;
            case "中等":
            case "中度受损":
                return 3;
            case "及格":
            case "重度受损":
                return 4;
            case "不及格":
            case "完全丧失":
                return 5;
            default:
                return 1; // 默认为能力完好
        }
    }

    /**
     * 生成PDF文件
     * 集成GBReportTemplate实现真正的PDF生成
     */
    private String generatePdfFile(AeaEvaluateReportVo report, List<AeaTaskEvaluateExecutionRecordVo> records) {
        try {
            log.info("开始生成PDF报告，报告编码: {}", report.getCode());

            // 1. 获取任务和评估详情信息
            AeaTaskEvaluateVo task = aeaTaskEvaluateMapper.selectVoById(report.getTaskId());
            LambdaQueryWrapper<AeaTaskEvaluateInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AeaTaskEvaluateInfo::getTaskId, report.getTaskId());
            List<AeaTaskEvaluateInfoVo> evaluateInfoVoList = aeaTaskEvaluateInfoMapper.selectVoList(queryWrapper);

            if (task == null || evaluateInfoVoList.isEmpty()) {
                throw new RuntimeException("任务或评估详情信息不存在");
            }

            // 2. 构建PDF生成所需的数据
            Map<String, String> reportData = buildReportData(report, task, evaluateInfoVoList, records);

            // 3. 调用GBReportTemplate生成PDF
            File pdfFile = gbReportTemplate.generateReportExec(
                reportData,
                "康养机构", // 机构名称，可以从配置或数据库获取
                report.getAssessorEvaluateUserId() != null ? report.getAssessorEvaluateUserId().toString() : "主评估员",
                report.getDeputyEvaluateUserId() != null ? report.getDeputyEvaluateUserId().toString() : "副评估员",
                report.getLocation() != null ? report.getLocation() : "评估地点",
                new ArrayList<>(), // 药物列表，根据实际需求填充
                report.getAssessorSign(), // 主评估员签名
                report.getDeputySign(),   // 副评估员签名
                report.getInformationProviderSign()    // 信息提供者签名
            );

            // 4. 生成文件访问URL
            String fileName = pdfFile.getName();
            String pdfUrl = "/uploads/reports/" + fileName;

            log.info("PDF报告生成成功，报告编码: {}, 文件路径: {}", report.getCode(), pdfUrl);

            return pdfUrl;

        } catch (Exception e) {
            log.error("生成PDF报告失败，报告编码: {}", report.getCode(), e);
            throw new RuntimeException("生成PDF报告失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建PDF报告生成所需的数据 - 基于Word模板结构的完整映射
     * 模板包含四个主要部分：
     * Section 1: 封面 + 表A.1 评估信息
     * Section 2: 表A.2 评估对象基本信息 + 表A.3 信息提供者信息 + 表A.4 疾病诊断用药 + 表A.5 健康相关问题
     * Section 3: 表B.1 自理能力评估 + 表B.2 基础运动能力评估 + 表B.3 精神状态评估 + 表B.4 感知觉与社会参与评估
     * Section 4: 表B.5 老年人能力总分 + 老年人能力评估报告
     */
    private Map<String, String> buildReportData(AeaEvaluateReportVo report, AeaTaskEvaluateVo task,
                                               List<AeaTaskEvaluateInfoVo> evaluateInfoVoList,
                                               List<AeaTaskEvaluateExecutionRecordVo> records) {
        Map<String, String> data = new HashMap<>();

        try {
            log.info("开始构建PDF报告数据，基于Word模板结构进行完整映射");

            // 获取评估详情信息（第一个，因为一个任务对应一个评估详情）
            AeaTaskEvaluateInfoVo evaluateInfo = evaluateInfoVoList.isEmpty() ? null : evaluateInfoVoList.get(0);

            /*=========================== SECTION 1: 封面 + 表A.1 评估信息 ===========================*/
            log.info("Section 1: 构建封面和评估信息表数据");

            // 封面信息
            data.put("eva_org", "康养机构"); // 评估机构
            data.put("elder_name", getElderName(evaluateInfo)); // 老人姓名
            data.put("address", report.getLocation() != null ? report.getLocation() : ""); // 评估地点
            data.put("assessor_name", buildAssessorNames(report)); // 评估人员

            // 表A.1 评估信息
            data.put("evaluate_code", report.getCode() != null ? report.getCode() : ""); // 评估编号
            data.put("evaluate_time", formatEvaluateTime(task)); // 评估时间
            data.put("reasonCode", task.getReasonCode() != null ? task.getReasonCode().toString() : ""); // 评估原因

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            data.put("evaBaseTime", task.getStartTime() != null ? sdf.format(task.getStartTime()) : ""); // 评估基准时间

            /*=========================== SECTION 2: 基本信息表组 ===========================*/
            log.info("Section 2: 构建基本信息表数据 (A.2-A.5)");

            // 按问卷分类提取基本信息数据
            Map<String, List<AeaTaskEvaluateExecutionRecordVo>> basicInfoByCategory =
                categorizeBasicInfoRecords(records);

            // 表A.2 评估对象基本信息表
            buildTableA2Data(data, basicInfoByCategory.get("BASIC_INFO"));

            // 表A.3 信息提供者及联系人信息表
            buildTableA3Data(data, basicInfoByCategory.get("PROVIDER_INFO"));

            // 表A.4 疾病诊断和用药情况表
            buildTableA4Data(data, basicInfoByCategory.get("DISEASE_INFO"));

            // 表A.5 健康相关问题
            buildTableA5Data(data, basicInfoByCategory.get("HEALTH_INFO"));

            /*=========================== SECTION 3: 能力评估表组 ===========================*/
            log.info("Section 3: 构建能力评估表数据 (B.1-B.4)");

            // 按评估维度分类提取评估数据
            Map<String, List<AeaTaskEvaluateExecutionRecordVo>> assessmentByDimension =
                categorizeAssessmentRecords(records);

            // 表B.1 自理能力评估表 (对应GBReportTemplate中的B.1.1-B.1.8)
            buildTableB1Data(data, assessmentByDimension.get("SELF_CARE"));

            // 表B.2 基础运动能力评估表 (对应GBReportTemplate中的B.2.1-B.2.4)
            buildTableB2Data(data, assessmentByDimension.get("BASIC_MOBILITY"));

            // 表B.3 精神状态评估表 (对应GBReportTemplate中的B.3.1-B.3.9)
            buildTableB3Data(data, assessmentByDimension.get("MENTAL_STATE"));

            // 表B.4 感知觉与社会参与评估表 (对应GBReportTemplate中的B.4.1-B.4.5)
            buildTableB4Data(data, assessmentByDimension.get("PERCEPTION_SOCIAL"));

            /*=========================== SECTION 4: 总分统计和评估报告 ===========================*/
            log.info("Section 4: 构建总分统计和评估报告数据 (B.5 + 报告)");

            // 表B.5 老年人能力总分 - 对应GBReportTemplate中的分数字段
            buildTableB5ScoreData(data, report);

            // 老年人能力评估报告 - 等级评定和调整
            buildAssessmentReportData(data, report, task);

            // 时间和签名信息
            buildTimeAndSignatureData(data, report, task);

            log.info("构建PDF报告数据完成，基于Word模板结构完整映射，数据项数量: {}", data.size());
            return data;

        } catch (Exception e) {
            log.error("构建PDF报告数据失败", e);
            throw new RuntimeException("构建PDF报告数据失败: " + e.getMessage());
        }
    }

    /*=========================== 数据构建辅助方法 ===========================*/

    /**
     * 获取老人姓名
     */
    private String getElderName(AeaTaskEvaluateInfoVo evaluateInfo) {
        if (evaluateInfo != null && evaluateInfo.getElderId() != null) {
            // 这里应该通过elderId查询老人信息获取姓名
            // 临时返回elderId，实际应该查询老人表获取姓名
            return "老人姓名_" + evaluateInfo.getElderId();
        }
        return "";
    }

    /**
     * 构建评估员姓名字符串
     */
    private String buildAssessorNames(AeaEvaluateReportVo report) {
        StringBuilder names = new StringBuilder();
        if (report.getAssessorEvaluateUserId() != null) {
            names.append("主评估员_").append(report.getAssessorEvaluateUserId());
        }
        if (report.getDeputyEvaluateUserId() != null) {
            if (names.length() > 0) names.append(",");
            names.append("副评估员_").append(report.getDeputyEvaluateUserId());
        }
        return names.toString();
    }

    /**
     * 格式化评估时间
     */
    private String formatEvaluateTime(AeaTaskEvaluateVo task) {
        if (task.getStartTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            return sdf.format(task.getStartTime());
        }
        return "";
    }

    /**
     * 按基本信息分类对执行记录进行分组
     */
    private Map<String, List<AeaTaskEvaluateExecutionRecordVo>> categorizeBasicInfoRecords(
            List<AeaTaskEvaluateExecutionRecordVo> records) {
        Map<String, List<AeaTaskEvaluateExecutionRecordVo>> categorized = new HashMap<>();

        if (records != null) {
            for (AeaTaskEvaluateExecutionRecordVo record : records) {
                String category = determineBasicInfoCategory(record);
                categorized.computeIfAbsent(category, k -> new ArrayList<>()).add(record);
            }
        }

        return categorized;
    }

    /**
     * 按评估维度对执行记录进行分组
     */
    private Map<String, List<AeaTaskEvaluateExecutionRecordVo>> categorizeAssessmentRecords(
            List<AeaTaskEvaluateExecutionRecordVo> records) {
        Map<String, List<AeaTaskEvaluateExecutionRecordVo>> categorized = new HashMap<>();

        if (records != null) {
            for (AeaTaskEvaluateExecutionRecordVo record : records) {
                String dimension = determineAssessmentDimension(record);
                categorized.computeIfAbsent(dimension, k -> new ArrayList<>()).add(record);
            }
        }

        return categorized;
    }

    /**
     * 确定基本信息记录的分类
     */
    private String determineBasicInfoCategory(AeaTaskEvaluateExecutionRecordVo record) {
        // 根据问题ID或问题内容判断属于哪个基本信息表
        // 这里需要根据实际的问卷结构来判断
        Long questionId = record.getQuestionId();

        if (questionId != null) {
            // 根据问题ID范围判断分类（示例逻辑，需要根据实际问卷调整）
            if (questionId >= 1 && questionId <= 20) {
                return "BASIC_INFO"; // 表A.2 评估对象基本信息
            } else if (questionId >= 21 && questionId <= 30) {
                return "PROVIDER_INFO"; // 表A.3 信息提供者信息
            } else if (questionId >= 31 && questionId <= 50) {
                return "DISEASE_INFO"; // 表A.4 疾病诊断用药
            } else if (questionId >= 51 && questionId <= 70) {
                return "HEALTH_INFO"; // 表A.5 健康相关问题
            }
        }

        return "BASIC_INFO"; // 默认分类
    }

    /**
     * 确定评估记录的维度分类
     */
    private String determineAssessmentDimension(AeaTaskEvaluateExecutionRecordVo record) {
        // 根据问题ID或问题内容判断属于哪个评估维度
        Long questionId = record.getQuestionId();

        if (questionId != null) {
            // 根据问题ID范围判断评估维度（示例逻辑，需要根据实际问卷调整）
            if (questionId >= 100 && questionId <= 120) {
                return "SELF_CARE"; // 自理能力评估
            } else if (questionId >= 121 && questionId <= 140) {
                return "BASIC_MOBILITY"; // 基础运动能力评估
            } else if (questionId >= 141 && questionId <= 170) {
                return "MENTAL_STATE"; // 精神状态评估
            } else if (questionId >= 171 && questionId <= 190) {
                return "PERCEPTION_SOCIAL"; // 感知觉与社会参与评估
            }
        }

        return "SELF_CARE"; // 默认分类
    }

    /*=========================== 表格数据构建方法 ===========================*/

    /**
     * 构建表A.2 评估对象基本信息表数据
     */
    private void buildTableA2Data(Map<String, String> data, List<AeaTaskEvaluateExecutionRecordVo> records) {
        if (records != null) {
            for (AeaTaskEvaluateExecutionRecordVo record : records) {
                // 根据GBReportTemplate中的字段映射
                mapBasicInfoFields(data, record);
            }
        }
    }

    /**
     * 构建表A.3 信息提供者及联系人信息表数据
     */
    private void buildTableA3Data(Map<String, String> data, List<AeaTaskEvaluateExecutionRecordVo> records) {
        if (records != null) {
            for (AeaTaskEvaluateExecutionRecordVo record : records) {
                // 映射信息提供者相关字段
                mapProviderInfoFields(data, record);
            }
        }
    }

    /**
     * 构建表A.4 疾病诊断和用药情况表数据
     */
    private void buildTableA4Data(Map<String, String> data, List<AeaTaskEvaluateExecutionRecordVo> records) {
        if (records != null) {
            for (AeaTaskEvaluateExecutionRecordVo record : records) {
                // 映射疾病诊断和用药字段
                mapDiseaseInfoFields(data, record);
            }
        }
    }

    /**
     * 构建表A.5 健康相关问题数据
     */
    private void buildTableA5Data(Map<String, String> data, List<AeaTaskEvaluateExecutionRecordVo> records) {
        if (records != null) {
            for (AeaTaskEvaluateExecutionRecordVo record : records) {
                // 映射健康相关问题字段
                mapHealthInfoFields(data, record);
            }
        }
    }

    /**
     * 构建表B.1 自理能力评估表数据
     */
    private void buildTableB1Data(Map<String, String> data, List<AeaTaskEvaluateExecutionRecordVo> records) {
        if (records != null) {
            // 对应GBReportTemplate中的B.1.1-B.1.8字段
            for (int i = 0; i < Math.min(records.size(), 8); i++) {
                AeaTaskEvaluateExecutionRecordVo record = records.get(i);
                String fieldKey = "B.1." + (i + 1);
                data.put(fieldKey, record.getScore() != null ? record.getScore().toString() : "0");
            }
        }
    }

    /**
     * 构建表B.2 基础运动能力评估表数据
     */
    private void buildTableB2Data(Map<String, String> data, List<AeaTaskEvaluateExecutionRecordVo> records) {
        if (records != null) {
            // 对应GBReportTemplate中的B.2.1-B.2.4字段
            for (int i = 0; i < Math.min(records.size(), 4); i++) {
                AeaTaskEvaluateExecutionRecordVo record = records.get(i);
                String fieldKey = "B.2." + (i + 1);
                data.put(fieldKey, record.getScore() != null ? record.getScore().toString() : "0");
            }
        }
    }

    /**
     * 构建表B.3 精神状态评估表数据
     */
    private void buildTableB3Data(Map<String, String> data, List<AeaTaskEvaluateExecutionRecordVo> records) {
        if (records != null) {
            // 对应GBReportTemplate中的B.3.1-B.3.9字段
            for (int i = 0; i < Math.min(records.size(), 9); i++) {
                AeaTaskEvaluateExecutionRecordVo record = records.get(i);
                String fieldKey = "B.3." + (i + 1);
                data.put(fieldKey, record.getScore() != null ? record.getScore().toString() : "0");
            }
        }
    }

    /**
     * 构建表B.4 感知觉与社会参与评估表数据
     */
    private void buildTableB4Data(Map<String, String> data, List<AeaTaskEvaluateExecutionRecordVo> records) {
        if (records != null) {
            // 对应GBReportTemplate中的B.4.1-B.4.5字段
            for (int i = 0; i < Math.min(records.size(), 5); i++) {
                AeaTaskEvaluateExecutionRecordVo record = records.get(i);
                String fieldKey = "B.4." + (i + 1);
                data.put(fieldKey, record.getScore() != null ? record.getScore().toString() : "0");
            }
        }
    }

    /**
     * 构建表B.5 老年人能力总分数据
     */
    private void buildTableB5ScoreData(Map<String, String> data, AeaEvaluateReportVo report) {
        // 对应GBReportTemplate中的分数字段
        data.put("selfScore", report.getSelfScore() != null ? report.getSelfScore().toString() : "0");
        data.put("baseScore", report.getBaseScore() != null ? report.getBaseScore().toString() : "0");
        data.put("mentionScore", report.getMentionScore() != null ? report.getMentionScore().toString() : "0");
        data.put("feelScore", report.getFeelScore() != null ? report.getFeelScore().toString() : "0");
        data.put("totalScore", report.getTotalScore() != null ? report.getTotalScore().toString() : "0");
    }

    /**
     * 构建评估报告数据（等级评定和调整）
     */
    private void buildAssessmentReportData(Map<String, String> data, AeaEvaluateReportVo report, AeaTaskEvaluateVo task) {
        // 等级信息
        data.put("firstLevel", report.getFirstLevel() != null ? report.getFirstLevel().toString() : "1");
        data.put("finalLevel", report.getFinalLevel() != null ? report.getFinalLevel().toString() : "1");

        // 调整依据
        data.put("adjustmentBasis", report.getAdjustmentBasis() != null ? report.getAdjustmentBasis() : "");

        // 评估原因
        data.put("reasonCode", task.getReasonCode() != null ? task.getReasonCode().toString() : "1");

        // 评估地点
        data.put("evaluate_adress", report.getLocation() != null ? report.getLocation() : "");
    }

    /**
     * 构建时间和签名数据
     */
    private void buildTimeAndSignatureData(Map<String, String> data, AeaEvaluateReportVo report, AeaTaskEvaluateVo task) {
        // 时间信息
        if (task.getStartTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String[] dateParts = sdf.format(task.getStartTime()).split("-");
            data.put("year", dateParts[0]);
            data.put("month", dateParts[1]);
            data.put("day", dateParts[2]);
            data.put("year2", dateParts[0]);
            data.put("month2", dateParts[1]);
            data.put("day2", dateParts[2]);
        }

        // 签名信息（这些会在GBReportTemplate中通过addSignatureImage方法处理）
        // 这里只是为了数据完整性而添加
        data.put("assessorSign", report.getAssessorSign() != null ? report.getAssessorSign() : "");
        data.put("deputySign", report.getDeputySign() != null ? report.getDeputySign() : "");
        data.put("informationProviderSign", report.getInformationProviderSign() != null ? report.getInformationProviderSign() : "");
    }

    /*=========================== 字段映射方法 ===========================*/

    /**
     * 映射基本信息字段到PDF表单域
     */
    private void mapBasicInfoFields(Map<String, String> data, AeaTaskEvaluateExecutionRecordVo record) {
        // 根据问题ID映射到对应的PDF字段
        Long questionId = record.getQuestionId();
        String answerContent = record.getContent();
        Long answerId = record.getAnswerId();

        if (questionId != null) {
            // 根据实际问卷结构映射字段（示例映射，需要根据实际情况调整）
            switch (questionId.intValue()) {
                case 1: // 姓名
                    data.put("name", answerContent != null ? answerContent : "");
                    break;
                case 2: // 性别
                    data.put("gender", answerId != null ? answerId.toString() : "");
                    break;
                case 3: // 出生日期
                    data.put("birthday", answerContent != null ? answerContent : "");
                    break;
                case 4: // 身高
                    data.put("height", answerContent != null ? answerContent : "");
                    break;
                case 5: // 体重
                    data.put("weight", answerContent != null ? answerContent : "");
                    break;
                case 6: // 民族
                    data.put("ethnic", answerId != null ? answerId.toString() : "");
                    break;
                case 7: // 宗教信仰
                    data.put("religion", answerId != null ? answerId.toString() : "");
                    break;
                case 8: // 身份证号
                    data.put("idnumber", answerContent != null ? answerContent : "");
                    break;
                case 9: // 文化程度
                    data.put("education", answerId != null ? answerId.toString() : "");
                    break;
                case 10: // 居住情况
                    data.put("residence", answerId != null ? answerId.toString() : "");
                    break;
                case 11: // 婚姻状况
                    data.put("marriage", answerId != null ? answerId.toString() : "");
                    break;
                case 12: // 医疗费用支付方式
                    data.put("medical", answerId != null ? answerId.toString() : "");
                    break;
                case 13: // 经济来源
                    data.put("income", answerId != null ? answerId.toString() : "");
                    break;
                // 可以继续添加更多字段映射
            }
        }
    }

    /**
     * 映射信息提供者字段
     */
    private void mapProviderInfoFields(Map<String, String> data, AeaTaskEvaluateExecutionRecordVo record) {
        Long questionId = record.getQuestionId();
        String answerContent = record.getContent();
        Long answerId = record.getAnswerId();

        if (questionId != null) {
            switch (questionId.intValue()) {
                case 21: // 信息提供者姓名
                    data.put("provideName", answerContent != null ? answerContent : "");
                    break;
                case 22: // 与老人关系
                    data.put("provideRelation", answerId != null ? answerId.toString() : "");
                    break;
                case 23: // 联系人姓名
                    data.put("relationName", answerContent != null ? answerContent : "");
                    break;
                case 24: // 联系人电话
                    data.put("relationMobile", answerContent != null ? answerContent : "");
                    break;
            }
        }
    }

    /**
     * 映射疾病诊断和用药字段
     */
    private void mapDiseaseInfoFields(Map<String, String> data, AeaTaskEvaluateExecutionRecordVo record) {
        Long questionId = record.getQuestionId();
        Long answerId = record.getAnswerId();

        if (questionId != null) {
            switch (questionId.intValue()) {
                case 31: // 疾病诊断
                    data.put("illness", answerId != null ? answerId.toString() : "");
                    break;
                // 可以添加更多疾病和用药相关字段
            }
        }
    }

    /**
     * 映射健康相关问题字段
     */
    private void mapHealthInfoFields(Map<String, String> data, AeaTaskEvaluateExecutionRecordVo record) {
        Long questionId = record.getQuestionId();
        Long answerId = record.getAnswerId();
        String answerContent = record.getContent();

        if (questionId != null) {
            switch (questionId.intValue()) {
                case 51: // 跌倒风险
                    data.put("fall", answerId != null ? answerId.toString() : "");
                    break;
                case 52: // 走失风险
                    data.put("lost", answerId != null ? answerId.toString() : "");
                    break;
                case 53: // 噎食风险
                    data.put("choking", answerId != null ? answerId.toString() : "");
                    break;
                case 54: // 自杀风险
                    data.put("suicide", answerId != null ? answerId.toString() : "");
                    break;
                case 55: // 其他风险
                    data.put("other", answerId != null ? answerId.toString() : "");
                    break;
                case 56: // 压疮风险
                    data.put("pressure", answerId != null ? answerId.toString() : "");
                    break;
                case 57: // 关节活动
                    data.put("joint", answerId != null ? answerId.toString() : "");
                    break;
                case 58: // 伤口/造口
                    data.put("wound", answerId != null ? answerId.toString() : "");
                    break;
                case 59: // 特殊护理
                    data.put("specialCare", answerId != null ? answerId.toString() : "");
                    break;
                case 60: // 疼痛
                    data.put("pain", answerId != null ? answerId.toString() : "");
                    break;
                case 61: // 口腔/牙齿
                    data.put("tooth", answerId != null ? answerId.toString() : "");
                    break;
                case 62: // 假牙
                    data.put("falseTooth", answerId != null ? answerId.toString() : "");
                    break;
                case 63: // 吞咽功能
                    data.put("swan", answerId != null ? answerId.toString() : "");
                    break;
                case 64: // BMI
                    data.put("BMI", answerId != null ? answerId.toString() : "");
                    break;
                case 65: // 清洁程度
                    data.put("clean", answerId != null ? answerId.toString() : "");
                    break;
                case 66: // 传染性疾病
                    data.put("disease", answerId != null ? answerId.toString() : "");
                    break;
                case 67: // 其他情况
                    data.put("situation", answerContent != null ? answerContent : "");
                    break;
            }
        }
    }

    /**
     * 集成的PDF报告生成方法 - 与问卷评估服务集成
     * 这个方法接收来自AeaQuestionnaireEvaluateServiceImpl的数据并生成PDF
     */
    @Transactional(rollbackFor = Exception.class)
    public String generatePdfReportIntegrated(Long reportId, Map<String, Object> collectedData) {
        try {
            log.info("开始集成PDF报告生成，报告ID: {}", reportId);

            // 1. 获取报告记录
            AeaEvaluateReportVo report = this.queryById(reportId);
            if (report == null) {
                throw new RuntimeException("报告记录不存在，报告ID: " + reportId);
            }

            // 2. 从收集的数据中提取各部分信息
            AeaTaskEvaluateVo task = (AeaTaskEvaluateVo) collectedData.get("taskEvaluate");
            Map<String, Object> basicInfoData = (Map<String, Object>) collectedData.get("basicInfoData");
            Map<String, Object> assessmentData = (Map<String, Object>) collectedData.get("assessmentData");
            List<AeaTaskEvaluateInfoVo> evaluateInfoList = (List<AeaTaskEvaluateInfoVo>) collectedData.get("evaluateInfoList");

            // 3. 收集所有执行记录
            List<AeaTaskEvaluateExecutionRecordVo> allRecords = collectAllExecutionRecords(basicInfoData, assessmentData);

            // 4. 构建PDF数据映射
            Map<String, String> pdfData = buildReportData(report, task, evaluateInfoList, allRecords);

            // 5. 生成PDF文件
            String pdfUrl = generateActualPdfReport(task, report, pdfData);

            // 6. 更新报告记录的PDF URL
            updateReportPdfUrl(reportId, pdfUrl);

            log.info("集成PDF报告生成成功，报告ID: {}, PDF URL: {}", reportId, pdfUrl);
            return pdfUrl;

        } catch (Exception e) {
            log.error("集成PDF报告生成失败，报告ID: {}", reportId, e);
            throw new RuntimeException("集成PDF报告生成失败: " + e.getMessage());
        }
    }

    /**
     * 收集所有执行记录
     */
    private List<AeaTaskEvaluateExecutionRecordVo> collectAllExecutionRecords(
            Map<String, Object> basicInfoData, Map<String, Object> assessmentData) {
        List<AeaTaskEvaluateExecutionRecordVo> allRecords = new ArrayList<>();

        // 收集基本信息数据
        if (basicInfoData != null) {
            for (Object value : basicInfoData.values()) {
                if (value instanceof List) {
                    List<AeaTaskEvaluateExecutionRecordVo> records = (List<AeaTaskEvaluateExecutionRecordVo>) value;
                    allRecords.addAll(records);
                }
            }
        }

        // 收集评估数据
        if (assessmentData != null) {
            for (Object value : assessmentData.values()) {
                if (value instanceof List) {
                    List<AeaTaskEvaluateExecutionRecordVo> records = (List<AeaTaskEvaluateExecutionRecordVo>) value;
                    allRecords.addAll(records);
                }
            }
        }

        log.info("收集到执行记录总数: {}", allRecords.size());
        return allRecords;
    }

    /**
     * 生成实际的PDF报告文件
     */
    private String generateActualPdfReport(AeaTaskEvaluateVo task, AeaEvaluateReportVo report, Map<String, String> pdfData) {
        try {
            // 调用GBReportTemplate生成PDF
            File pdfFile = gbReportTemplate.generateReportExec(
                pdfData,
                "康养机构", // 机构名称
                getAssessorName(report.getAssessorEvaluateUserId()), // 主评估员
                getAssessorName(report.getDeputyEvaluateUserId()), // 副评估员
                report.getLocation() != null ? report.getLocation() : "", // 评估地点
                Collections.emptyList(), // 药物列表
                report.getAssessorSign() != null ? report.getAssessorSign() : "", // 主评估员签名
                report.getDeputySign() != null ? report.getDeputySign() : "", // 副评估员签名
                report.getInformationProviderSign() != null ? report.getInformationProviderSign() : "" // 信息提供者签名
            );

            if (pdfFile == null || !pdfFile.exists()) {
                throw new RuntimeException("PDF文件生成失败");
            }

            // 上传到OSS或返回本地路径
            String pdfUrl = uploadPdfToStorage(pdfFile, report.getCode());

            log.info("PDF文件生成成功，文件大小: {} bytes, URL: {}", pdfFile.length(), pdfUrl);
            return pdfUrl;

        } catch (Exception e) {
            log.error("生成PDF文件失败", e);
            throw new RuntimeException("生成PDF文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取评估员姓名
     */
    private String getAssessorName(Long assessorId) {
        if (assessorId == null) {
            return "";
        }
        // 这里应该查询用户表获取评估员姓名
        // 临时返回ID，实际应该查询用户表
        return "评估员_" + assessorId;
    }

    /**
     * 上传PDF到存储系统
     */
    private String uploadPdfToStorage(File pdfFile, String reportCode) {
        try {
            // 这里应该实现实际的文件上传逻辑（OSS、本地存储等）
            // 临时返回本地文件路径
            String relativePath = "/reports/" + reportCode + ".pdf";
            log.info("PDF文件上传完成，相对路径: {}", relativePath);
            return relativePath;
        } catch (Exception e) {
            log.error("上传PDF文件失败", e);
            throw new RuntimeException("上传PDF文件失败: " + e.getMessage());
        }
    }

    /**
     * 更新报告记录的PDF URL
     */
    private void updateReportPdfUrl(Long reportId, String pdfUrl) {
        try {
            AeaEvaluateReport updateReport = new AeaEvaluateReport();
            updateReport.setId(reportId);
            updateReport.setReportUrl(pdfUrl);
            updateReport.setUpdateTime(new Date());

            int updateResult = baseMapper.updateById(updateReport);
            if (updateResult <= 0) {
                throw new RuntimeException("更新报告PDF URL失败");
            }

            log.info("更新报告PDF URL成功，报告ID: {}, PDF URL: {}", reportId, pdfUrl);
        } catch (Exception e) {
            log.error("更新报告PDF URL失败，报告ID: {}", reportId, e);
            throw new RuntimeException("更新报告PDF URL失败: " + e.getMessage());
        }
    }
}
